import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router';
import { Button } from '@repo/ui/components/button';
// import { Card, CardContent } from '@repo/ui/components/card';
// import { Badge } from '@repo/ui/components/badge';
import { Skeleton } from '@repo/ui/components/skeleton';
import { Alert, AlertDescription } from '@repo/ui/components/alert';
import { Input } from '@repo/ui/components/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@repo/ui/components/select';
import { Checkbox } from '@repo/ui/components/checkbox';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@repo/ui/components/table';
import { ArrowLeft, Menu, Search, ChevronLeft, ChevronRight, ArrowRight, X, RotateCcw } from 'lucide-react';
import { USER_ROUTES } from '@/app.routes';
import axiosInstance from '@/lib/axios';

interface User {
  id: number;
  first_name: string;
  last_name: string;
  emp_id: string;
  email: string;
  department: string;
  location: string;
  survey: string | number;
  response_id?: string;
  survey_response?: string | null; // Can be null as seen in the console
  metadata?: any; // Metadata object containing all custom fields
  rater?: {
    first_name: string;
    last_name: string;
    emp_id: string;
  };
  target?: {
    first_name: string;
    last_name: string;
    emp_id: string;
    metadata?: any; // Metadata object for target user
  };
}

interface SurveyInfo {
  id: string;
  title: string;
  isPairsEditable: boolean;
  isDeclinable: boolean;
}

interface StatusCounts {
  TODO: number;
  PROG: number;
  SUBM: number;
  DECL: number;
}

const Pairings: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [users, setUsers] = useState<User[]>([]);
  const [surveyInfo, setSurveyInfo] = useState<SurveyInfo | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [status, setStatus] = useState('TODO');
  const [statusCounts, setStatusCounts] = useState<StatusCounts>({
    TODO: 0,
    PROG: 0,
    SUBM: 0,
    DECL: 0
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUsers, setSelectedUsers] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const [showSidebar, setShowSidebar] = useState(false);
  const [ordering] = useState('first_name');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [pageSize] = useState(15);
  const [metadataLabels, setMetadataLabels] = useState<any[]>([]);
  const [selectedMetadataField, setSelectedMetadataField] = useState<string>('');
  const [selectedMetadataValue, setSelectedMetadataValue] = useState<string>('All');
  const [availableMetadataValues, setAvailableMetadataValues] = useState<string[]>([]);

  useEffect(() => {
    if (id) {
      fetchSurveyInfo();
      fetchStatusCounts();
      fetchMetadataLabels();
    }
  }, [id]);

  useEffect(() => {
    if (id) {
      fetchUsers();
    }
  }, [id, status, searchTerm, ordering, currentPage, selectedMetadataField, selectedMetadataValue]);

  // Update available metadata values when field changes
  useEffect(() => {
    if (selectedMetadataField) {
      const selectedLabel = metadataLabels.find(label => label.field === selectedMetadataField);
      setAvailableMetadataValues(selectedLabel?.options || []);
      setSelectedMetadataValue('All'); // Reset value when field changes
    } else {
      setAvailableMetadataValues([]);
      setSelectedMetadataValue('All');
    }
  }, [selectedMetadataField, metadataLabels]);

  const fetchSurveyInfo = async () => {
    try {
      const response = await axiosInstance.get(`/survey/index/${id}/`);
      setSurveyInfo(response.data);
    } catch (err) {
      console.error('Error fetching survey info:', err);
      setError('Failed to load survey information');
    }
  };

  const fetchStatusCounts = async () => {
    try {
      // Fetch counts for each status
      const statuses = ['TODO', 'PROG', 'SUBM', 'DECL'];
      const counts: StatusCounts = { TODO: 0, PROG: 0, SUBM: 0, DECL: 0 };

      for (const statusKey of statuses) {
        const response = await axiosInstance.get('/survey/pairing/', {
          params: {
            index_id: id,
            status: statusKey,
            page_size: 1,
            exclude_disabled: true
          }
        });
        counts[statusKey as keyof StatusCounts] = response.data.count_items || 0;
      }

      setStatusCounts(counts);
    } catch (err) {
      console.error('Error fetching status counts:', err);
    }
  };

  const fetchMetadataLabels = async () => {
    try {
      const response = await axiosInstance.get('/staff/customers/metadata-labels/', {
        params: {
          survey_id: id,
          exclude_disabled: true
        }
      });
      setMetadataLabels(response.data.labels || []);
    } catch (err) {
      console.error('Error fetching metadata labels:', err);
      setMetadataLabels([]);
    }
  };

  const fetchUsers = async () => {
    try {
      setIsLoading(true);

      // Build metadata filter params
      const metadataParams: any = {};
      if (selectedMetadataField && selectedMetadataValue && selectedMetadataValue !== 'All') {
        metadataParams[selectedMetadataField] = selectedMetadataValue;
      }

      // Use different endpoints based on status (similar to legacy app)
      const response = status !== "ADD"
        ? await axiosInstance.get('/survey/pairing/', {
            params: {
              index_id: id,
              status,
              page_size: pageSize,
              page: currentPage,
              ordering,
              exclude_disabled: true,
              ...(searchTerm && { search: searchTerm }),
              ...metadataParams
            }
          })
        : await axiosInstance.get('/survey/pairing/add/', {
            params: {
              index_id: id,
              page_size: pageSize,
              page: currentPage,
              ordering,
              ...(searchTerm && { search: searchTerm }),
              ...metadataParams
            }
          });

      // Debug: Log the response to see the actual structure
      console.log('Pairing API response:', response.data);
      if (response.data.results && response.data.results.length > 0) {
        console.log('First user data:', response.data.results[0]);
      }

      setUsers(response.data.results || []);
      setTotalPages(response.data.count_pages || 1);
      setTotalItems(response.data.count_items || 0);
      setError(null);
    } catch (err) {
      console.error('Error fetching users:', err);
      setError('Failed to load pairings');
    } finally {
      setIsLoading(false);
    }
  };

  const handleStartSurvey = async (user: User) => {
    try {
      // Check if response_id already exists
      const responseId = user.response_id || user.survey_response;

      if (responseId && user.survey) {
        // Navigate directly using the existing response_id and survey_id
        navigate(USER_ROUTES().dashboard.upwardReview.getTakeSurveyUrl(responseId, user.survey.toString()));
      } else if (user.survey) {
        // If no response_id exists, create a new survey response (similar to legacy app)
        console.log('Creating new survey response for user:', user);

        const createResponsePayload = {
          survey: user.survey,
          pairing: user.id,
          // status: "PROG", // Uncomment if needed
        };

        const response = await axiosInstance.post('/survey/survey-response/', createResponsePayload);

        // Navigate using the newly created response_id
        navigate(USER_ROUTES().dashboard.upwardReview.getTakeSurveyUrl(
          response.data.id,
          response.data.survey || user.survey.toString()
        ));
      } else {
        console.error('No survey ID found for user:', user);
        setError('Survey information not found. Please contact support.');
      }
    } catch (err) {
      console.error('Error starting survey:', err);
      setError('Failed to start survey');
    }
  };

  const handleAddUsersToSurvey = async () => {
    try {
      if (selectedUsers.length === 0) {
        setError('Please select at least one user to add');
        return;
      }

      // Add multiple users to survey pairing (similar to legacy app)
      const promises = selectedUsers.map(userId =>
        axiosInstance.post('/survey/pairing/', {
          index: id, // Use 'index' instead of 'index_id' based on the error message
          target: userId
        })
      );

      await Promise.all(promises);

      // Clear selections
      setSelectedUsers([]);
      setSelectAll(false);

      // Refresh the users list to show updated data
      fetchUsers();

      // Switch back to TODO status to show the newly added pairings
      setStatus('TODO');
    } catch (err) {
      console.error('Error adding users to survey:', err);
      setError('Failed to add users to survey');
    }
  };

  const handleUserSelect = (userId: number) => {
    setSelectedUsers(prev =>
      prev.includes(userId)
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked);
    if (checked) {
      setSelectedUsers(users.map(user => user.id));
    } else {
      setSelectedUsers([]);
    }
  };

  const handleDeclineUser = async (user: User) => {
    if (!confirm(`Are you sure you want to decline the pairing with ${user.target?.first_name} ${user.target?.last_name}?`)) {
      return;
    }

    try {
      // Create response if it doesn't exist
      let responseId = user.response_id;
      if (!responseId) {
        const createResponsePayload = {
          survey: user.survey,
          pairing: user.id,
        };
        const response = await axiosInstance.post('/survey/survey-response/', createResponsePayload);
        responseId = response.data.id;
      }

      // Decline the pairing
      const declinePayload = {
        survey: user.survey,
        pairing: user.id,
        resourcetype: "SurveyResponseUpward",
      };

      await axiosInstance.post(`/survey/survey-response/${responseId}/decline/`, declinePayload);

      // Refresh data
      fetchUsers();
      fetchSurveyInfo();

      setError('Pairing declined successfully.');
    } catch (err) {
      console.error('Error declining pairing:', err);
      setError('Failed to decline pairing. Please try again.');
    }
  };

  const handleReinstateUser = async (user: User) => {
    if (!confirm(`Are you sure you want to reinstate the pairing with ${user.target?.first_name} ${user.target?.last_name}?`)) {
      return;
    }

    try {
      // Create response if it doesn't exist
      let responseId = user.response_id;
      if (!responseId) {
        const createResponsePayload = {
          survey: user.survey,
          pairing: user.id,
        };
        const response = await axiosInstance.post('/survey/survey-response/', createResponsePayload);
        responseId = response.data.id;
      }

      // Reinstate the pairing
      const reinstatePayload = {
        survey: user.survey,
        pairing: user.id,
      };

      await axiosInstance.post(`/survey/survey-response/${responseId}/reinstate/`, reinstatePayload);

      // Refresh data and switch to TODO status
      fetchUsers();
      fetchSurveyInfo();
      setStatus('TODO');

      setError('Pairing reinstated successfully.');
    } catch (err) {
      console.error('Error reinstating pairing:', err);
      setError('Failed to reinstate pairing. Please try again.');
    }
  };

  const getStatusLabel = (statusKey: string) => {
    const statusMap = {
      TODO: 'To-Do',
      PROG: 'In-Progress',
      SUBM: 'Completed',
      DECL: 'Declined'
    };
    return statusMap[statusKey as keyof typeof statusMap] || statusKey;
  };

  if (error) {
    return (
      <div className="p-6 bg-white dark:bg-gray-900">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  // Sidebar Component
  const Sidebar = () => (
    <div className={`
      fixed inset-y-0 left-0 z-40 w-80 bg-white dark:bg-gray-800 border-r dark:border-gray-700 transform transition-transform duration-300 ease-in-out
      ${showSidebar ? 'translate-x-0' : '-translate-x-full'}
      lg:translate-x-0 lg:static lg:inset-0
    `}>
      <div className="p-6 space-y-6">
        {/* Back Button */}
        <Button
          variant="ghost"
          onClick={() => navigate(USER_ROUTES().dashboard.surveyList)}
          className="w-full justify-start"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>

        {/* Survey Status */}
        <div>
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-4">Survey Status</h3>
          <div className="space-y-2">
            {[
              { key: 'TODO', label: 'To-Do', count: statusCounts.TODO },
              { key: 'PROG', label: 'In-Progress', count: statusCounts.PROG },
              { key: 'SUBM', label: 'Completed', count: statusCounts.SUBM },
              { key: 'DECL', label: 'Declined', count: statusCounts.DECL }
            ].map((item) => (
              <div
                key={item.key}
                className={`
                  flex justify-between items-center p-3 rounded cursor-pointer transition-colors
                  ${status === item.key
                    ? 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 border-l-4 border-green-500 font-semibold'
                    : 'hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
                  }
                `}
                onClick={() => {
                  setStatus(item.key);
                  setCurrentPage(1); // Reset to first page when changing status
                  setShowSidebar(false);
                }}
              >
                <span className="text-sm">{item.label}</span>
                <span className="font-semibold">
                  {isLoading ? <Skeleton className="h-4 w-6" /> : item.count}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Add Reviewee Button */}
        {surveyInfo?.isPairsEditable && (
          <div>
            <hr className="my-4" />
            <Button
              className="w-full bg-green-600 hover:bg-green-700 text-white border-green-600 hover:border-green-700"
              variant="outline"
              disabled={status === 'ADD'}
              onClick={() => {
                setStatus('ADD');
                setCurrentPage(1); // Reset to first page when switching to ADD mode
              }}
            >
              Add Reviewee
            </Button>
            <div className="mt-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded text-sm text-gray-700 dark:text-gray-300">
              <p>Please click <strong>Add Reviewee</strong> to add a new person to rate.</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className="flex w-full h-screen bg-gray-50 dark:bg-gray-900">
      {/* Sidebar */}
      <Sidebar />

      {/* Overlay for mobile */}
      {showSidebar && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden"
          onClick={() => setShowSidebar(false)}
        />
      )}

      {/* Main Content */}
      <div className="flex-1 w-full flex flex-col lg:ml-0">
        {/* Header */}
        <div className="bg-orange-500 dark:bg-orange-600 text-white px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="sm"
                className="lg:hidden mr-2 text-white hover:bg-orange-600 dark:hover:bg-orange-700"
                onClick={() => setShowSidebar(true)}
              >
                <Menu className="h-4 w-4" />
              </Button>
              <div>
                <h1 className="text-xl font-semibold">
                  {surveyInfo?.title || 'Survey'} - {getStatusLabel(status)}
                </h1>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white dark:bg-gray-800 border-b dark:border-gray-700 px-6 py-4">
          <div className="flex items-center gap-4 flex-wrap">
            {/* Metadata Field Selector */}
            <Select
              value={selectedMetadataField}
              onValueChange={(value) => {
                setSelectedMetadataField(value);
                setCurrentPage(1); // Reset to first page when filtering
              }}
            >
              <SelectTrigger className="w-40">
                <SelectValue placeholder="Select Filter" />
              </SelectTrigger>
              <SelectContent>
                {metadataLabels.map((label) => (
                  <SelectItem key={label.field} value={label.field}>
                    {label.display_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Metadata Value Selector */}
            <Select
              value={selectedMetadataValue}
              onValueChange={(value) => {
                setSelectedMetadataValue(value);
                setCurrentPage(1); // Reset to first page when filtering
              }}
              disabled={!selectedMetadataField}
            >
              <SelectTrigger className="w-40">
                <SelectValue placeholder="All" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="All">All</SelectItem>
                {availableMetadataValues.map((option: string) => (
                  <SelectItem key={option} value={option}>
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Selected Count and Add Button (only show in ADD mode) */}
            {status === 'ADD' && selectedUsers.length > 0 && (
              <>
                <span className="text-blue-600 dark:text-blue-400 font-medium">
                  {selectedUsers.length} User{selectedUsers.length > 1 ? 's' : ''} Selected
                </span>
                <Button
                  onClick={handleAddUsersToSurvey}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  Add
                </Button>
              </>
            )}

            {/* Search Input */}
            <div className="flex-1 max-w-md ml-auto">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 h-4 w-4" />
                <Input
                  placeholder="Search for name, email or emp"
                  value={searchTerm}
                  onChange={(e) => {
                    setSearchTerm(e.target.value);
                    setCurrentPage(1); // Reset to first page when searching
                  }}
                  className="pl-10"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Content Area */}
        <div className="flex-1 p-6">
          {isLoading ? (
            <div className="space-y-4">
              {Array(5).fill(0).map((_, index) => (
                <Skeleton key={index} className="h-12 w-full" />
              ))}
            </div>
          ) : users.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500 dark:text-gray-400">
                There are no items to show. <button
                  className="text-blue-600 dark:text-blue-400 hover:underline"
                  onClick={() => {
                    setStatus('ADD');
                    setCurrentPage(1); // Reset to first page when switching to ADD mode
                  }}
                >
                  Click here
                </button> add a new reviewee.
              </p>
            </div>
          ) : (
            <div className="bg-white dark:bg-gray-800 rounded-lg border dark:border-gray-700">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">
                      <Checkbox
                        checked={selectAll}
                        onCheckedChange={handleSelectAll}
                      />
                    </TableHead>
                    <TableHead>No.</TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Class Year</TableHead>
                    <TableHead>Department</TableHead>
                    <TableHead>Title</TableHead>
                    <TableHead>Practice Group</TableHead>
                    <TableHead>Location</TableHead>
                    {status !== 'ADD' && <TableHead>Actions</TableHead>}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {users.map((user, index) => (
                    <TableRow key={user.id}>
                      <TableCell>
                        <Checkbox
                          checked={selectedUsers.includes(user.id)}
                          onCheckedChange={() => handleUserSelect(user.id)}
                        />
                      </TableCell>
                      <TableCell>{(currentPage - 1) * pageSize + index + 1}.</TableCell>
                      <TableCell
                        className={status !== 'ADD' ? "cursor-pointer text-blue-600 dark:text-blue-400 hover:underline" : ""}
                        onClick={() => status !== 'ADD' && handleStartSurvey(user)}
                      >
                        {status === 'ADD'
                          ? `${user.first_name} ${user.last_name}`
                          : `${user.target?.first_name} ${user.target?.last_name}`
                        }
                      </TableCell>
                      <TableCell>
                        {status === 'ADD'
                          ? (user.metadata?.class_year || '-')
                          : (user.target?.metadata?.class_year || '-')
                        }
                      </TableCell>
                      <TableCell>
                        {status === 'ADD'
                          ? (user.department || user.metadata?.department || '-')
                          : (user.target?.metadata?.department || user.department || '-')
                        }
                      </TableCell>
                      <TableCell>
                        {status === 'ADD'
                          ? (user.metadata?.current_level || '-')
                          : (user.target?.metadata?.current_level || '-')
                        }
                      </TableCell>
                      <TableCell>
                        {status === 'ADD'
                          ? (user.metadata?.practice_group || '-')
                          : (user.target?.metadata?.practice_group || '-')
                        }
                      </TableCell>
                      <TableCell>
                        {status === 'ADD'
                          ? (user.metadata?.office_location || '-')
                          : (user.target?.metadata?.office_location || '-')
                        }
                      </TableCell>
                      {status !== 'ADD' && (
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {(status === 'TODO' || status === 'PROG') && (
                              <>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => handleStartSurvey(user)}
                                  className="p-1 h-8 w-8 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                                >
                                  <ArrowRight className="h-4 w-4" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => handleDeclineUser(user)}
                                  className="p-1 h-8 w-8 text-red-600 hover:text-red-700 hover:bg-red-50"
                                >
                                  <X className="h-4 w-4" />
                                </Button>
                              </>
                            )}
                            {status === 'DECL' && (
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => handleReinstateUser(user)}
                                className="p-1 h-8 w-8 text-green-600 hover:text-green-700 hover:bg-green-50"
                                title="Reinstate"
                              >
                                <RotateCcw className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      )}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between px-6 py-4 border-t dark:border-gray-700">
                  <div className="text-sm text-gray-700 dark:text-gray-300">
                    Showing {(currentPage - 1) * pageSize + 1} to {Math.min(currentPage * pageSize, totalItems)} of {totalItems} results
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      disabled={currentPage === 1}
                    >
                      <ChevronLeft className="h-4 w-4" />
                      Previous
                    </Button>
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      Page {currentPage} of {totalPages}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                      disabled={currentPage === totalPages}
                    >
                      Next
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Pairings;
